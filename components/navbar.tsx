"use client"

import { useState } from "react"
import Link from "next/link"
import { Search, Menu, X, User } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { cn } from "@/lib/utils"
import { usePathname } from "next/navigation"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

export default function Navbar() {
  const [isSearchOpen, setIsSearchOpen] = useState(false)
  const pathname = usePathname()

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background">
      <div className="container flex h-16 items-center px-4 sm:px-6">
        <Link href="/" className="flex items-center gap-2">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="h-6 w-6 text-primary"
          >
            <path d="M16.5 9.4l-9-5.19M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z" />
            <polyline points="3.29 7 12 12 20.71 7" />
            <line x1="12" x2="12" y1="22" y2="12" />
          </svg>
          <span className="hidden font-bold text-primary sm:inline-block">AffiliateMarket</span>
        </Link>

        {isSearchOpen ? (
          <div className="relative mx-auto flex w-full max-w-md items-center lg:max-w-lg">
            <Input
              className="h-10 w-full rounded-full border-none bg-muted pl-10 pr-4 focus-visible:ring-primary"
              placeholder="Search for affiliates by niche or location..."
              autoFocus
            />
            <Search className="absolute left-3 h-4 w-4 text-muted-foreground" />
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-1 h-8 w-8"
              onClick={() => setIsSearchOpen(false)}
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Close search</span>
            </Button>
          </div>
        ) : (
          <nav className="mx-auto hidden gap-6 md:flex">
            <Link
              href="/"
              className={cn(
                "text-sm font-medium transition-colors hover:text-primary",
                pathname === "/" ? "text-primary" : "text-muted-foreground",
              )}
            >
              Home
            </Link>
            <Link
              href="/search"
              className={cn(
                "text-sm font-medium transition-colors hover:text-primary",
                pathname === "/search" ? "text-primary" : "text-muted-foreground",
              )}
            >
              Find Affiliates
            </Link>
            <Link
              href="/listings"
              className={cn(
                "text-sm font-medium transition-colors hover:text-primary",
                pathname === "/listings" ? "text-primary" : "text-muted-foreground",
              )}
            >
              Listings
            </Link>
            <Link
              href="/docs/affiliate-program"
              className={cn(
                "text-sm font-medium transition-colors hover:text-primary",
                pathname.startsWith("/docs") ? "text-primary" : "text-muted-foreground",
              )}
            >
              Documentation
            </Link>
          </nav>
        )}

        <div className="ml-auto flex items-center gap-2">
          {!isSearchOpen && (
            <Button
              variant="ghost"
              size="icon"
              className="mr-1 hidden h-9 w-9 md:flex"
              onClick={() => setIsSearchOpen(true)}
            >
              <Search className="h-4 w-4" />
              <span className="sr-only">Search</span>
            </Button>
          )}
          <Link href="/become-affiliate" className="hidden md:block">
            <Button variant="outline" size="sm">
              Become an Affiliate
            </Button>
          </Link>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-9 w-9 rounded-full">
                <Avatar className="h-8 w-8">
                  <AvatarImage src="/placeholder.svg" alt="User" />
                  <AvatarFallback>
                    <User className="h-4 w-4" />
                  </AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuItem asChild>
                <Link href="/dashboard">Dashboard</Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href="/settings">Settings</Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link href="/help">Help Center</Link>
              </DropdownMenuItem>
              <DropdownMenuItem>Sign out</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" className="h-9 w-9 md:hidden">
                <Menu className="h-5 w-5" />
                <span className="sr-only">Toggle menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[300px] sm:w-[400px]">
              <nav className="grid gap-6 py-6">
                <Link href="/" className="flex items-center gap-2 text-lg font-semibold">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-6 w-6 text-primary"
                  >
                    <path d="M16.5 9.4l-9-5.19M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z" />
                    <polyline points="3.29 7 12 12 20.71 7" />
                    <line x1="12" x2="12" y1="22" y2="12" />
                  </svg>
                  AffiliateMarket
                </Link>
                <div className="grid gap-3">
                  <Link href="/" className="flex items-center gap-2 text-base font-medium">
                    Home
                  </Link>
                  <Link href="/search" className="flex items-center gap-2 text-base font-medium">
                    Find Affiliates
                  </Link>
                  <Link href="/how-it-works" className="flex items-center gap-2 text-base font-medium">
                    How It Works
                  </Link>
                  <Link href="/docs/affiliate-program" className="flex items-center gap-2 text-base font-medium">
                    Documentation
                  </Link>
                  <Link href="/become-affiliate" className="flex items-center gap-2 text-base font-medium">
                    Become an Affiliate
                  </Link>
                </div>
                <div className="grid gap-3">
                  <Link href="/dashboard" className="flex items-center gap-2 text-base font-medium">
                    Dashboard
                  </Link>
                  <Link href="/settings" className="flex items-center gap-2 text-base font-medium">
                    Settings
                  </Link>
                  <Link href="/help" className="flex items-center gap-2 text-base font-medium">
                    Help Center
                  </Link>
                </div>
                <div className="mt-4">
                  <Button className="w-full">Sign out</Button>
                </div>
              </nav>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  )
}
