'use client';

import React, { useEffect, useState } from 'react';
import { mdxComponents } from './mdx-components';

// Create a type for the serialized content
type SerializedMDXContent = any;

interface MDXContentProps {
  source: SerializedMDXContent;
}

// Client component wrapper for MDXRemote that handles dynamic loading
export default function MDXContent({ source }: MDXContentProps) {
  const [MDXComponent, setMDXComponent] = useState<React.ComponentType<any> | null>(null);

  useEffect(() => {
    // Only import MDXRemote on the client side
    import('next-mdx-remote').then(({ MDXRemote }) => {
      setMDXComponent(() => (props: any) => <MDXRemote {...props} />);
    });
  }, []);

  if (!MDXComponent) {
    return <div className="prose prose-stone max-w-none">Loading content...</div>;
  }

  return (
    <div className="prose prose-stone max-w-none">
      <MDXComponent {...source} components={mdxComponents} />
    </div>
  );
}
