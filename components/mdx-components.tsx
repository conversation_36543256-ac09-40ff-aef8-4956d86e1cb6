import Link from 'next/link';
import { MDXRemoteProps } from 'next-mdx-remote';

// Component to handle links - using next/link for internal links and regular anchor tags for external links
const CustomLink = ({ href, children, ...props }: React.AnchorHTMLAttributes<HTMLAnchorElement>) => {
  const isInternalLink = href && (href.startsWith('/') || href.startsWith('#'));

  if (isInternalLink) {
    return (
      <Link href={href} {...props}>
        {children}
      </Link>
    );
  }

  return (
    <a href={href} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800 underline" {...props}>
      {children}
    </a>
  );
};

// Define styled components for MDX content
const H1 = ({ children }: { children: React.ReactNode }) => (
  <h1 className="text-4xl font-bold tracking-tight text-gray-900 mt-8 mb-6">{children}</h1>
);

const H2 = ({ children }: { children: React.ReactNode }) => (
  <h2 className="text-2xl font-semibold text-gray-800 mt-10 mb-4 border-b pb-2">{children}</h2>
);

const H3 = ({ children }: { children: React.ReactNode }) => (
  <h3 className="text-xl font-medium text-gray-800 mt-8 mb-3">{children}</h3>
);

const Paragraph = ({ children }: { children: React.ReactNode }) => (
  <p className="my-4 text-gray-700">{children}</p>
);

const UlList = ({ children }: { children: React.ReactNode }) => (
  <ul className="list-disc pl-8 my-4 text-gray-700 space-y-2">{children}</ul>
);

const OlList = ({ children }: { children: React.ReactNode }) => (
  <ol className="list-decimal pl-8 my-4 text-gray-700 space-y-2">{children}</ol>
);

const ListItem = ({ children }: { children: React.ReactNode }) => (
  <li className="my-1 pl-2">{children}</li>
);

const CodeBlock = ({ children }: { children: React.ReactNode }) => (
  <pre className="my-6 p-4 bg-gray-100 rounded-md overflow-x-auto text-sm shadow-sm">{children}</pre>
);

const InlineCode = ({ children }: { children: React.ReactNode }) => (
  <code className="px-1.5 py-0.5 bg-gray-100 rounded text-sm font-mono text-blue-600">{children}</code>
);

const Blockquote = ({ children }: { children: React.ReactNode }) => (
  <blockquote className="border-l-4 border-gray-300 pl-4 my-4 italic text-gray-700">{children}</blockquote>
);

// Create a map of HTML elements to custom React components
export const mdxComponents = {
  h1: H1,
  h2: H2,
  h3: H3,
  p: Paragraph,
  ul: UlList,
  ol: OlList,
  li: ListItem,
  pre: CodeBlock,
  code: InlineCode,
  a: CustomLink,
  blockquote: Blockquote,
};
