"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/components/ui/use-toast"
import { Loader2, <PERSON><PERSON><PERSON>, Trash2 } from "lucide-react"

type Product = {
  id: string
  name: string
  status: "draft" | "active"
  price: number
  commissionRate: number
  category: string
}

export function ManageProducts() {
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  // TODO: Replace with actual products fetch
  const products: Product[] = [
    {
      id: "1",
      name: "Sample Product",
      status: "active",
      price: 99.99,
      commissionRate: 10,
      category: "electronics"
    }
  ]

  const handleDelete = async (productId: string) => {
    try {
      setIsLoading(true)
      // TODO: Implement delete API call
      console.log("Deleting product:", productId)
      
      toast({
        title: "Success!",
        description: "Product has been deleted successfully.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Something went wrong. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Your Products</CardTitle>
        </CardHeader>
        <CardContent>
          {products.length === 0 ? (
            <p className="text-center text-muted-foreground py-4">
              No products found. Create your first product to get started.
            </p>
          ) : (
            <div className="space-y-4">
              {products.map((product) => (
                <Card key={product.id}>
                  <CardContent className="flex items-center justify-between p-4">
                    <div className="space-y-1">
                      <h3 className="font-medium">{product.name}</h3>
                      <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                        <span>${product.price}</span>
                        <span>{product.commissionRate}% Commission</span>
                        <span className="capitalize">{product.category}</span>
                        <span className="capitalize">{product.status}</span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => console.log("Edit product:", product.id)}
                        disabled={isLoading}
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="destructive"
                        size="icon"
                        onClick={() => handleDelete(product.id)}
                        disabled={isLoading}
                      >
                        {isLoading ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <Trash2 className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
