import Link from "next/link"
import { Facebook, Instagram, Linkedin, Twitter } from "lucide-react"

export default function Footer() {
  return (
    <footer className="border-t bg-background">
      <div className="container grid gap-8 px-4 py-10 md:grid-cols-2 lg:grid-cols-4">
        <div>
          <h3 className="mb-4 text-lg font-semibold">Support</h3>
          <ul className="grid gap-2">
            <li>
              <Link href="/help" className="text-sm text-muted-foreground hover:text-foreground">
                Help Center
              </Link>
            </li>
            <li>
              <Link href="/safety" className="text-sm text-muted-foreground hover:text-foreground">
                Safety Information
              </Link>
            </li>
            <li>
              <Link href="/cancellation" className="text-sm text-muted-foreground hover:text-foreground">
                Cancellation Options
              </Link>
            </li>
            <li>
              <Link href="/covid" className="text-sm text-muted-foreground hover:text-foreground">
                COVID-19 Resources
              </Link>
            </li>
          </ul>
        </div>
        <div>
          <h3 className="mb-4 text-lg font-semibold">Community</h3>
          <ul className="grid gap-2">
            <li>
              <Link href="/blog" className="text-sm text-muted-foreground hover:text-foreground">
                Blog
              </Link>
            </li>
            <li>
              <Link href="/forum" className="text-sm text-muted-foreground hover:text-foreground">
                Forum
              </Link>
            </li>
            <li>
              <Link href="/events" className="text-sm text-muted-foreground hover:text-foreground">
                Events
              </Link>
            </li>
            <li>
              <Link href="/partners" className="text-sm text-muted-foreground hover:text-foreground">
                Partner Program
              </Link>
            </li>
          </ul>
        </div>
        <div>
          <h3 className="mb-4 text-lg font-semibold">Affiliate</h3>
          <ul className="grid gap-2">
            <li>
              <Link href="/become-affiliate" className="text-sm text-muted-foreground hover:text-foreground">
                Become an Affiliate
              </Link>
            </li>
            <li>
              <Link href="/resources" className="text-sm text-muted-foreground hover:text-foreground">
                Resources
              </Link>
            </li>
            <li>
              <Link href="/guidelines" className="text-sm text-muted-foreground hover:text-foreground">
                Community Guidelines
              </Link>
            </li>
            <li>
              <Link href="/success-stories" className="text-sm text-muted-foreground hover:text-foreground">
                Success Stories
              </Link>
            </li>
          </ul>
        </div>
        <div>
          <h3 className="mb-4 text-lg font-semibold">About</h3>
          <ul className="grid gap-2">
            <li>
              <Link href="/about" className="text-sm text-muted-foreground hover:text-foreground">
                About Us
              </Link>
            </li>
            <li>
              <Link href="/careers" className="text-sm text-muted-foreground hover:text-foreground">
                Careers
              </Link>
            </li>
            <li>
              <Link href="/press" className="text-sm text-muted-foreground hover:text-foreground">
                Press
              </Link>
            </li>
            <li>
              <Link href="/contact" className="text-sm text-muted-foreground hover:text-foreground">
                Contact
              </Link>
            </li>
          </ul>
        </div>
      </div>
      <div className="border-t">
        <div className="container flex flex-col items-center justify-between gap-4 px-4 py-6 md:flex-row">
          <div className="flex items-center gap-4">
            <Link href="/terms" className="text-xs text-muted-foreground hover:text-foreground">
              Terms of Service
            </Link>
            <Link href="/privacy" className="text-xs text-muted-foreground hover:text-foreground">
              Privacy Policy
            </Link>
            <Link href="/sitemap" className="text-xs text-muted-foreground hover:text-foreground">
              Sitemap
            </Link>
          </div>
          <div className="flex items-center gap-4">
            <Link href="https://facebook.com" className="text-muted-foreground hover:text-foreground">
              <Facebook className="h-4 w-4" />
              <span className="sr-only">Facebook</span>
            </Link>
            <Link href="https://twitter.com" className="text-muted-foreground hover:text-foreground">
              <Twitter className="h-4 w-4" />
              <span className="sr-only">Twitter</span>
            </Link>
            <Link href="https://instagram.com" className="text-muted-foreground hover:text-foreground">
              <Instagram className="h-4 w-4" />
              <span className="sr-only">Instagram</span>
            </Link>
            <Link href="https://linkedin.com" className="text-muted-foreground hover:text-foreground">
              <Linkedin className="h-4 w-4" />
              <span className="sr-only">LinkedIn</span>
            </Link>
          </div>
          <div className="text-xs text-muted-foreground">
            © {new Date().getFullYear()} AffiliateMarket, Inc. All rights reserved.
          </div>
        </div>
      </div>
    </footer>
  )
}

