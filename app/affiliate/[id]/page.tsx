"use client"

import dynamic from "next/dynamic"
import { useState, useEffect } from "react"
import Image from "next/image"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  CalendarIcon,
  Check,
  ChevronLeft,
  Facebook,
  Instagram,
  Linkedin,
  MapPin,
  MessageSquare,
  Share2,
  Star,
  Twitter,
} from "lucide-react"
import { cn } from "@/lib/utils"

// Import components with SSR disabled
const Tabs = dynamic(() => import("@/components/ui/tabs").then((mod) => mod.Tabs), {
  ssr: false,
})
const TabsList = dynamic(() => import("@/components/ui/tabs").then((mod) => mod.TabsList), {
  ssr: false,
})
const TabsTrigger = dynamic(() => import("@/components/ui/tabs").then((mod) => mod.TabsTrigger), {
  ssr: false,
})
const TabsContent = dynamic(() => import("@/components/ui/tabs").then((mod) => mod.TabsContent), {
  ssr: false,
})

// Import date-related components with SSR disabled
const DatePickerWrapper = dynamic(() => import("@/components/date-picker-wrapper"), {
  ssr: false,
  loading: () => (
    <Button variant="outline" className="w-full justify-start text-left font-normal text-muted-foreground">
      <CalendarIcon className="mr-2 h-4 w-4" />
      Select a date
    </Button>
  ),
})

export default function AffiliatePage({ params }: { params: { id: string } }) {
  const [isClient, setIsClient] = useState(false)

  // Set isClient to true after component mounts
  useEffect(() => {
    setIsClient(true)
  }, [])

  // This would normally come from an API call using the ID
  const affiliate = {
    id: params.id,
    name: "Sarah Johnson",
    title: "E-commerce Affiliate Marketing Specialist",
    image: "/placeholder.svg?height=400&width=400",
    coverImage: "/placeholder.svg?height=400&width=1200",
    niche: "E-commerce",
    location: "New York, NY",
    bio: "I'm a results-driven affiliate marketer with over 5 years of experience in the e-commerce space. I specialize in driving high-converting traffic to online stores through strategic content marketing, email campaigns, and social media promotion.",
    metrics: [
      { label: "Average ROI", value: "215%" },
      { label: "Clients Served", value: "50+" },
      { label: "Conversion Rate", value: "18%" },
      { label: "Years Experience", value: "5+" },
    ],
    skills: ["Email Marketing", "Content Creation", "Social Media", "SEO", "PPC Advertising", "Analytics"],
    rating: 4.9,
    reviews: 124,
    reviewsData: [
      {
        id: 1,
        author: "John Smith",
        avatar: "/placeholder.svg?height=50&width=50",
        rating: 5,
        date: "March 2023",
        content:
          "Sarah helped us increase our sales by 35% in just two months. Her email marketing campaigns were particularly effective. Highly recommend!",
      },
      {
        id: 2,
        author: "Emily Wong",
        avatar: "/placeholder.svg?height=50&width=50",
        rating: 5,
        date: "February 2023",
        content:
          "Working with Sarah was a game-changer for our online store. She understood our target audience perfectly and created content that really resonated with them.",
      },
      {
        id: 3,
        author: "Michael Brown",
        avatar: "/placeholder.svg?height=50&width=50",
        rating: 4,
        date: "January 2023",
        content:
          "Great experience overall. Sarah is professional, responsive, and delivers results. The only reason for 4 stars instead of 5 is that it took a bit longer than expected to see significant results.",
      },
    ],
    portfolio: [
      {
        title: "Fashion Retailer Campaign",
        description: "Increased sales by 45% through targeted email marketing and social media promotion.",
        image: "/placeholder.svg?height=200&width=300",
      },
      {
        title: "Tech Gadget Launch",
        description: "Generated over $100K in affiliate sales during a 2-week product launch period.",
        image: "/placeholder.svg?height=200&width=300",
      },
      {
        title: "Home Goods Promotion",
        description: "Created content strategy that improved conversion rates by 25% for a home goods brand.",
        image: "/placeholder.svg?height=200&width=300",
      },
    ],
  }

  // Render a loading state or simplified version until client-side components are ready
  if (!isClient) {
    return (
      <div className="flex flex-col">
        <div className="relative h-[200px] w-full sm:h-[300px]">
          <Image
            src={affiliate.coverImage || "/placeholder.svg"}
            alt={`${affiliate.name} cover`}
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-t from-background/80 to-transparent" />
          <Link
            href="/search"
            className="absolute left-4 top-4 flex items-center gap-1 rounded-full bg-background/80 px-3 py-1 text-sm backdrop-blur-sm transition-colors hover:bg-background"
          >
            <ChevronLeft className="h-4 w-4" />
            Back to search
          </Link>
        </div>
        <div className="container px-4 py-6">
          <div className="flex flex-col items-start sm:flex-row sm:items-center sm:gap-4">
            <div className="relative -mt-16 h-24 w-24 overflow-hidden rounded-full border-4 border-background sm:h-32 sm:w-32">
              <Image src={affiliate.image || "/placeholder.svg"} alt={affiliate.name} fill className="object-cover" />
            </div>
            <div className="mt-4 sm:mt-0">
              <h1 className="text-2xl font-bold sm:text-3xl">{affiliate.name}</h1>
              <p className="text-lg text-muted-foreground">{affiliate.title}</p>
              <p className="mt-2 text-sm text-muted-foreground">Loading profile details...</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col">
      {/* Cover Image */}
      <div className="relative h-[200px] w-full sm:h-[300px]">
        <Image
          src={affiliate.coverImage || "/placeholder.svg"}
          alt={`${affiliate.name} cover`}
          fill
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 bg-gradient-to-t from-background/80 to-transparent" />
        <Link
          href="/search"
          className="absolute left-4 top-4 flex items-center gap-1 rounded-full bg-background/80 px-3 py-1 text-sm backdrop-blur-sm transition-colors hover:bg-background"
        >
          <ChevronLeft className="h-4 w-4" />
          Back to search
        </Link>
      </div>

      <div className="container px-4 py-6">
        <div className="grid gap-6 md:grid-cols-[2fr_1fr]">
          <div>
            {/* Profile Header */}
            <div className="flex flex-col items-start sm:flex-row sm:items-center sm:gap-4">
              <div className="relative -mt-16 h-24 w-24 overflow-hidden rounded-full border-4 border-background sm:h-32 sm:w-32">
                <Image src={affiliate.image || "/placeholder.svg"} alt={affiliate.name} fill className="object-cover" />
              </div>
              <div className="mt-4 sm:mt-0">
                <div className="flex items-center gap-2">
                  <h1 className="text-2xl font-bold sm:text-3xl">{affiliate.name}</h1>
                  <Button variant="ghost" size="icon" className="rounded-full">
                    <Share2 className="h-4 w-4" />
                    <span className="sr-only">Share profile</span>
                  </Button>
                </div>
                <p className="text-lg text-muted-foreground">{affiliate.title}</p>
                <div className="mt-2 flex flex-wrap items-center gap-3">
                  <Badge variant="outline">{affiliate.niche}</Badge>
                  <div className="flex items-center text-sm text-muted-foreground">
                    <MapPin className="mr-1 h-3 w-3" />
                    {affiliate.location}
                  </div>
                  <div className="flex items-center text-sm">
                    <Star className="mr-1 h-4 w-4 fill-primary text-primary" />
                    <span>
                      {affiliate.rating} ({affiliate.reviews} reviews)
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <Tabs defaultValue="about" className="mt-6">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="about">About</TabsTrigger>
                <TabsTrigger value="portfolio">Portfolio</TabsTrigger>
                <TabsTrigger value="reviews">Reviews</TabsTrigger>
                <TabsTrigger value="contact">Contact</TabsTrigger>
              </TabsList>
              <TabsContent value="about" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>About {affiliate.name}</CardTitle>
                    <CardDescription>Professional background and expertise</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div>
                      <h3 className="mb-2 font-semibold">Bio</h3>
                      <p className="text-muted-foreground">{affiliate.bio}</p>
                    </div>
                    <Separator />
                    <div>
                      <h3 className="mb-4 font-semibold">Performance Metrics</h3>
                      <div className="grid grid-cols-2 gap-4 sm:grid-cols-4">
                        {affiliate.metrics.map((metric, index) => (
                          <Card key={index} className="border-none bg-muted/50">
                            <CardContent className="p-4 text-center">
                              <p className="text-2xl font-bold text-primary">{metric.value}</p>
                              <p className="text-sm text-muted-foreground">{metric.label}</p>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </div>
                    <Separator />
                    <div>
                      <h3 className="mb-2 font-semibold">Skills & Expertise</h3>
                      <div className="flex flex-wrap gap-2">
                        {affiliate.skills.map((skill, index) => (
                          <Badge key={index} variant="secondary">
                            {skill}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              <TabsContent value="portfolio" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Portfolio</CardTitle>
                    <CardDescription>Past campaigns and success stories</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
                      {affiliate.portfolio.map((item, index) => (
                        <Card key={index} className="overflow-hidden">
                          <div className="aspect-video relative">
                            <Image
                              src={item.image || "/placeholder.svg"}
                              alt={item.title}
                              fill
                              className="object-cover"
                            />
                          </div>
                          <CardContent className="p-4">
                            <h3 className="font-semibold">{item.title}</h3>
                            <p className="mt-1 text-sm text-muted-foreground">{item.description}</p>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              <TabsContent value="reviews" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Client Reviews</CardTitle>
                    <CardDescription>Feedback from previous clients</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {affiliate.reviewsData.map((review) => (
                        <div key={review.id} className="space-y-2">
                          <div className="flex items-start justify-between">
                            <div className="flex items-center gap-2">
                              <div className="relative h-10 w-10 overflow-hidden rounded-full">
                                <Image
                                  src={review.avatar || "/placeholder.svg"}
                                  alt={review.author}
                                  fill
                                  className="object-cover"
                                />
                              </div>
                              <div>
                                <p className="font-medium">{review.author}</p>
                                <p className="text-xs text-muted-foreground">{review.date}</p>
                              </div>
                            </div>
                            <div className="flex items-center">
                              {Array.from({ length: 5 }).map((_, i) => (
                                <Star
                                  key={i}
                                  className={cn(
                                    "h-4 w-4",
                                    i < review.rating ? "fill-primary text-primary" : "text-muted-foreground",
                                  )}
                                />
                              ))}
                            </div>
                          </div>
                          <p className="text-sm text-muted-foreground">{review.content}</p>
                          <Separator className="mt-4" />
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              <TabsContent value="contact" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Contact {affiliate.name}</CardTitle>
                    <CardDescription>Send a message to discuss your project</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <form className="space-y-4">
                      <div className="grid gap-4 sm:grid-cols-2">
                        <div className="space-y-2">
                          <label htmlFor="name" className="text-sm font-medium">
                            Your Name
                          </label>
                          <Input id="name" placeholder="Enter your name" />
                        </div>
                        <div className="space-y-2">
                          <label htmlFor="email" className="text-sm font-medium">
                            Email Address
                          </label>
                          <Input id="email" type="email" placeholder="Enter your email" />
                        </div>
                      </div>
                      <div className="space-y-2">
                        <label htmlFor="subject" className="text-sm font-medium">
                          Subject
                        </label>
                        <Input id="subject" placeholder="What is this regarding?" />
                      </div>
                      <div className="space-y-2">
                        <label htmlFor="message" className="text-sm font-medium">
                          Message
                        </label>
                        <Textarea
                          id="message"
                          placeholder="Describe your project, goals, and requirements..."
                          rows={5}
                        />
                      </div>
                      <Button type="submit" className="w-full">
                        Send Message
                      </Button>
                    </form>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>

          <div className="space-y-6">
            {/* Hiring Form */}
            <Card>
              <CardHeader>
                <CardTitle>Hire {affiliate.name.split(" ")[0]}</CardTitle>
                <CardDescription>Complete this form to start the hiring process</CardDescription>
              </CardHeader>
              <CardContent>
                <form className="space-y-4">
                  <div className="space-y-2">
                    <label htmlFor="project-details" className="text-sm font-medium">
                      Project Details
                    </label>
                    <Textarea id="project-details" placeholder="Describe your products and project goals..." rows={4} />
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="budget" className="text-sm font-medium">
                      Budget Range
                    </label>
                    <select
                      id="budget"
                      className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    >
                      <option value="">Select a budget range</option>
                      <option value="1000-3000">$1,000 - $3,000</option>
                      <option value="3000-5000">$3,000 - $5,000</option>
                      <option value="5000-10000">$5,000 - $10,000</option>
                      <option value="10000+">$10,000+</option>
                    </select>
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="timeline" className="text-sm font-medium">
                      Timeline
                    </label>
                    <select
                      id="timeline"
                      className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    >
                      <option value="">Select a timeline</option>
                      <option value="1-month">1 month</option>
                      <option value="3-months">3 months</option>
                      <option value="6-months">6 months</option>
                      <option value="ongoing">Ongoing</option>
                    </select>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Preferred Start Date</label>
                    <DatePickerWrapper />
                  </div>
                  <Button className="w-full">Submit Hiring Request</Button>
                </form>
              </CardContent>
            </Card>

            {/* Social Links */}
            <Card>
              <CardHeader>
                <CardTitle>Connect</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex justify-between">
                  <Button variant="outline" size="icon" className="rounded-full">
                    <Linkedin className="h-4 w-4" />
                    <span className="sr-only">LinkedIn</span>
                  </Button>
                  <Button variant="outline" size="icon" className="rounded-full">
                    <Twitter className="h-4 w-4" />
                    <span className="sr-only">Twitter</span>
                  </Button>
                  <Button variant="outline" size="icon" className="rounded-full">
                    <Instagram className="h-4 w-4" />
                    <span className="sr-only">Instagram</span>
                  </Button>
                  <Button variant="outline" size="icon" className="rounded-full">
                    <Facebook className="h-4 w-4" />
                    <span className="sr-only">Facebook</span>
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button variant="outline" className="w-full justify-start">
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Message Directly
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Check className="mr-2 h-4 w-4" />
                  Save to Favorites
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}

