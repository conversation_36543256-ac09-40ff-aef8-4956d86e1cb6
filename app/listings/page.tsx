"use client"

import { <PERSON>R<PERSON> } from "lucide-react"
import { CreateProductForm } from "@/components/forms/create-product-form"
import { ManageProducts } from "@/components/forms/manage-products"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { useState } from "react"

export default function ListingsPage() {
  const [activeView, setActiveView] = useState<"create" | "manage" | null>(null)

  return (
    <section className="bg-muted/50 py-16">
      <div className="container px-4">
        <div className="mb-10 text-center">
          <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">Listings</h2>
          <p className="mt-4 text-lg text-muted-foreground">
            A simple process to find and hire the perfect affiliate for your business
          </p>
          <div className="mt-8 flex justify-center gap-4">
            <Button
              size="lg"
              onClick={() => setActiveView("create")}
              variant={activeView === "create" ? "default" : "outline"}
            >
              Create New Product
            </Button>
            <Button
              size="lg"
              onClick={() => setActiveView("manage")}
              variant={activeView === "manage" ? "default" : "outline"}
            >
              Manage Products
            </Button>
          </div>
        </div>

        {activeView && (
          <Card className="p-6">
            {activeView === "create" ? <CreateProductForm /> : <ManageProducts />}
          </Card>
        )}

        {!activeView && <div className="grid gap-8 md:grid-cols-3">
          {[
            {
              step: "1",
              title: "Search",
              description:
                "Browse our marketplace and filter affiliates by niche, location, and performance metrics.",
            },
            {
              step: "2",
              title: "Review",
              description:
                "Examine detailed profiles, past performance, and client reviews to find your perfect match.",
            },
            {
              step: "3",
              title: "Hire",
              description: "Contact affiliates directly, discuss your needs, and formalize your partnership.",
            },
          ].map((item, index) => (
            <div key={index} className="relative flex flex-col items-center p-6 text-center">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary text-primary-foreground">
                <span className="text-xl font-bold">{item.step}</span>
              </div>
              <h3 className="mt-4 text-xl font-semibold">{item.title}</h3>
              <p className="mt-2 text-muted-foreground">{item.description}</p>
              {index < 2 && (
                <ArrowRight className="absolute -right-4 top-10 hidden h-8 w-8 text-primary/30 md:block" />
              )}
            </div>
          ))}
        </div>}
      </div>
    </section>
  )
}
