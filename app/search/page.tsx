"use client"

import dynamic from "next/dynamic"
import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Star, MapPin, SearchIcon } from "lucide-react"
import { Separator } from "@/components/ui/separator"
import { Label } from "@/components/ui/label"

// Import Slider with SSR disabled
const Slider = dynamic(() => import("@/components/ui/slider").then((mod) => mod.Slider), {
  ssr: false,
})

// Import Tabs with SSR disabled
const Tabs = dynamic(() => import("@/components/ui/tabs").then((mod) => mod.Tabs), {
  ssr: false,
})
const TabsList = dynamic(() => import("@/components/ui/tabs").then((mod) => mod.TabsList), {
  ssr: false,
})
const TabsTrigger = dynamic(() => import("@/components/ui/tabs").then((mod) => mod.TabsTrigger), {
  ssr: false,
})
const TabsContent = dynamic(() => import("@/components/ui/tabs").then((mod) => mod.TabsContent), {
  ssr: false,
})

export default function SearchPage() {
  const [view, setView] = useState<"grid" | "map">("grid")
  const [minRoi, setMinRoi] = useState([100])
  const [isClient, setIsClient] = useState(false)

  // Set isClient to true after component mounts
  useState(() => {
    setIsClient(true)
  })

  // Render a loading state or simplified version until client-side components are ready
  if (!isClient) {
    return (
      <div className="flex flex-col">
        <div className="border-b bg-muted/30">
          <div className="container px-4 py-4">
            <h1 className="text-2xl font-bold tracking-tight">Find the Perfect Affiliate</h1>
            <p className="mt-1 text-muted-foreground">Browse and filter our marketplace of top-performing affiliates</p>
          </div>
        </div>
        <div className="container px-4 py-6">
          <div className="h-[600px] flex items-center justify-center">
            <p>Loading search interface...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col">
      <div className="border-b bg-muted/30">
        <div className="container px-4 py-4">
          <h1 className="text-2xl font-bold tracking-tight">Find the Perfect Affiliate</h1>
          <p className="mt-1 text-muted-foreground">Browse and filter our marketplace of top-performing affiliates</p>
        </div>
      </div>

      <div className="container grid gap-6 px-4 py-6 md:grid-cols-[280px_1fr] lg:grid-cols-[300px_1fr]">
        {/* Filters Sidebar */}
        <div className="space-y-6">
          <div>
            <h2 className="mb-2 font-semibold">Search</h2>
            <div className="relative">
              <SearchIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input type="search" placeholder="Search affiliates..." className="pl-8" />
            </div>
          </div>

          <Separator />

          <div>
            <h2 className="mb-2 font-semibold">Niche</h2>
            <Select>
              <SelectTrigger>
                <SelectValue placeholder="Select a niche" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Niches</SelectItem>
                <SelectItem value="ecommerce">E-commerce</SelectItem>
                <SelectItem value="fashion">Fashion</SelectItem>
                <SelectItem value="tech">Tech</SelectItem>
                <SelectItem value="health">Health & Wellness</SelectItem>
                <SelectItem value="finance">Finance</SelectItem>
                <SelectItem value="education">Education</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Separator />

          <div>
            <h2 className="mb-2 font-semibold">Location</h2>
            <Input type="text" placeholder="City or country" />
          </div>

          <Separator />

          <div>
            <div className="mb-2 flex items-center justify-between">
              <h2 className="font-semibold">Minimum ROI</h2>
              <span className="text-sm text-muted-foreground">{minRoi}%</span>
            </div>
            <Slider defaultValue={[100]} max={500} step={10} value={minRoi} onValueChange={setMinRoi} />
            <div className="mt-1 flex justify-between text-xs text-muted-foreground">
              <span>0%</span>
              <span>500%</span>
            </div>
          </div>

          <Separator />

          <div>
            <h2 className="mb-2 font-semibold">Experience Level</h2>
            <div className="space-y-2">
              {["Beginner", "Intermediate", "Expert"].map((level) => (
                <div key={level} className="flex items-center space-x-2">
                  <Checkbox id={`level-${level.toLowerCase()}`} />
                  <Label htmlFor={`level-${level.toLowerCase()}`}>{level}</Label>
                </div>
              ))}
            </div>
          </div>

          <Separator />

          <div>
            <h2 className="mb-2 font-semibold">Specialties</h2>
            <div className="space-y-2">
              {[
                "Social Media Marketing",
                "Email Marketing",
                "Content Creation",
                "SEO",
                "PPC Advertising",
                "Influencer Marketing",
              ].map((specialty) => (
                <div key={specialty} className="flex items-center space-x-2">
                  <Checkbox id={`specialty-${specialty.toLowerCase().replace(/\s+/g, "-")}`} />
                  <Label htmlFor={`specialty-${specialty.toLowerCase().replace(/\s+/g, "-")}`}>{specialty}</Label>
                </div>
              ))}
            </div>
          </div>

          <Separator />

          <Button className="w-full">Apply Filters</Button>
          <Button variant="outline" className="w-full">
            Reset Filters
          </Button>
        </div>

        {/* Results */}
        <div className="space-y-6">
          <Tabs defaultValue="grid" className="w-full" onValueChange={(value) => setView(value as "grid" | "map")}>
            <div className="flex items-center justify-between">
              <p className="text-sm text-muted-foreground">Showing 24 results</p>
              <TabsList className="grid w-[200px] grid-cols-2">
                <TabsTrigger value="grid">Grid View</TabsTrigger>
                <TabsTrigger value="map">Map View</TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="grid" className="mt-6">
              <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
                {Array.from({ length: 9 }).map((_, index) => {
                  const affiliateData = {
                    id: index + 1,
                    name: [
                      "Sarah Johnson",
                      "Michael Chen",
                      "Emma Rodriguez",
                      "David Kim",
                      "Olivia Smith",
                      "James Wilson",
                      "Sophia Garcia",
                      "Ethan Brown",
                      "Ava Martinez",
                    ][index % 9],
                    image: "/placeholder.svg?height=300&width=300",
                    niche: [
                      "E-commerce",
                      "Fashion",
                      "Tech",
                      "Health & Wellness",
                      "Finance",
                      "Education",
                      "Beauty",
                      "Travel",
                      "Food",
                    ][index % 9],
                    location: [
                      "New York",
                      "San Francisco",
                      "Los Angeles",
                      "Chicago",
                      "Miami",
                      "Seattle",
                      "Austin",
                      "Boston",
                      "Denver",
                    ][index % 9],
                    metric:
                      index % 3 === 0
                        ? `Average ROI: ${150 + index * 10}%`
                        : index % 3 === 1
                          ? `Conversion Rate: ${12 + index}%`
                          : `Monthly Sales: $${30 + index * 5}K+`,
                    rating: (4.5 + (index % 5) * 0.1).toFixed(1),
                    reviews: 50 + index * 10,
                  }

                  return (
                    <Link href={`/affiliate/${affiliateData.id}`} key={index}>
                      <Card className="overflow-hidden transition-all hover:shadow-md">
                        <div className="aspect-square relative">
                          <Image
                            src={affiliateData.image || "/placeholder.svg"}
                            alt={affiliateData.name}
                            fill
                            className="object-cover transition-transform hover:scale-105"
                          />
                        </div>
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <Badge className="mb-2">{affiliateData.niche}</Badge>
                            <div className="flex items-center text-sm text-muted-foreground">
                              <MapPin className="mr-1 h-3 w-3" />
                              <span>{affiliateData.location}</span>
                            </div>
                          </div>
                          <h3 className="font-semibold">{affiliateData.name}</h3>
                          <div className="mt-1 flex items-center text-sm text-muted-foreground">
                            <Star className="mr-1 h-4 w-4 fill-primary text-primary" />
                            <span>
                              {affiliateData.rating} ({affiliateData.reviews} reviews)
                            </span>
                          </div>
                          <p className="mt-2 text-sm font-medium text-primary">{affiliateData.metric}</p>
                          <Button variant="outline" className="mt-3 w-full">
                            View Profile
                          </Button>
                        </CardContent>
                      </Card>
                    </Link>
                  )
                })}
              </div>
              <div className="mt-8 flex justify-center">
                <Button variant="outline" className="mx-1">
                  1
                </Button>
                <Button variant="outline" className="mx-1">
                  2
                </Button>
                <Button variant="outline" className="mx-1">
                  3
                </Button>
                <span className="flex items-center px-2">...</span>
                <Button variant="outline" className="mx-1">
                  8
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="map" className="mt-6">
              <div className="relative h-[600px] w-full rounded-lg border bg-muted">
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center">
                    <Image
                      src="/placeholder.svg?height=600&width=800"
                      alt="Map view"
                      width={800}
                      height={600}
                      className="rounded-lg"
                    />
                    <p className="mt-4 text-sm text-muted-foreground">Interactive map showing affiliate locations</p>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}

