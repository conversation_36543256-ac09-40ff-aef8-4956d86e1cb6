import fs from 'fs/promises';
import path from 'path';
import { marked } from 'marked';
import { notFound } from 'next/navigation';

// Define TypeScript types for the page props
interface PageProps {
  params: {
    slug: string;
  };
}

export default async function DocumentationPage({ params }: PageProps) {
  // Define the path to the Markdown file based on the slug parameter
  const filePath = path.join(process.cwd(), 'public', 'docs', `${params.slug}.md`);
  
  let content = '';
  let documentExists = true;
  
  try {
    // Attempt to read the Markdown file from the filesystem
    const fileContent = await fs.readFile(filePath, 'utf8');
    
    // Parse the Markdown content into HTML using marked
    // Ensure we handle the case where marked might return a Promise
    const parsedContent = marked(fileContent);
    content = typeof parsedContent === 'string' ? parsedContent : await parsedContent;
  } catch (error) {
    // Handle file not found or other errors
    documentExists = false;
    
    // You can also use Next.js's notFound() to show a 404 page
    // Uncomment the line below to use this approach instead of showing the custom message
    // notFound();
  }
  
  return (
    <div className="container mx-auto">
      <header className="p-4 bg-gray-100 mb-4">
        Documentation Header
      </header>
      
      <div className="flex flex-col md:flex-row gap-6">
        <aside className="w-full md:w-1/4 p-4 border-r">
          Sidebar Navigation Placeholder
        </aside>
        
        <main className="flex-1 p-4 prose max-w-none">
          {documentExists ? (
            // Render the Markdown content as HTML
            // WARNING: Using dangerouslySetInnerHTML has security implications!
            // In a production environment, consider:
            // 1. Using a library like DOMPurify to sanitize the HTML
            // 2. Using a safer library like react-markdown instead of marked+dangerouslySetInnerHTML
            // 3. Only rendering content from trusted sources
            <div 
              dangerouslySetInnerHTML={{ __html: content }}
              className="prose prose-stone max-w-none"
            />
          ) : (
            <div className="text-center py-10">
              <h2 className="text-2xl font-bold text-gray-800 mb-4">Document Not Found</h2>
              <p className="text-gray-600">
                The documentation page you're looking for doesn't exist.
              </p>
              <p className="text-gray-600 mt-2">
                Please check the URL or navigate using the sidebar.
              </p>
            </div>
          )}
        </main>
      </div>
      
      <footer className="p-4 bg-gray-100 mt-4">
        Documentation Footer
      </footer>
    </div>
  );
}

// Generate static metadata for the page
export async function generateMetadata({ params }: PageProps) {
  // Create a title from the slug with proper capitalization
  const title = params.slug
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
  
  return {
    title: `${title} | Documentation`,
  };
}
