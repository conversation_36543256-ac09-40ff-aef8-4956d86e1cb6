
import fs from 'fs/promises';
import path from 'path';
import { notFound } from 'next/navigation';
import { serialize } from 'next-mdx-remote/serialize';
import MDXContent from '@/components/mdx-content';

/**
 * Server Component for rendering the affiliate program documentation
 * This component reads the Markdown file from /public/docs, serializes it using next-mdx-remote,
 * and renders it with custom MDX components
 */
export default async function AffiliateDocumentationPage() {
  // Define the path to the Markdown file
  const filePath = path.join(process.cwd(), 'public', 'docs', 'affiliate-program.md');
  
  try {
    // Read the Markdown file content
    const markdownContent = await fs.readFile(filePath, 'utf8');
    
    // Serialize the Markdown content to MDX
    const mdxSource = await serialize(markdownContent, {
      // Optional: Add rehype/remark plugins if needed
      mdxOptions: {
        /* 
        rehypePlugins: [[rehypeSlug], [rehypeAutolinkHeadings]],
        remarkPlugins: [],
        */
      },
      // Provide any data that should be available in the MDX content
      scope: {},
    });
    
    return (
      <div className="container mx-auto px-4">
        <header className="py-6 border-b mb-6">
          <div className="flex items-center text-sm text-gray-500 mb-2">
            <a href="/" className="hover:text-blue-600">Home</a>
            <span className="mx-2">›</span>
            <a href="/docs" className="hover:text-blue-600">Documentation</a>
            <span className="mx-2">›</span>
            <span className="text-gray-800">Affiliate Program</span>
          </div>
          <h1 className="text-3xl font-bold text-gray-900">Affiliate Program Documentation</h1>
        </header>
        
        <div className="flex flex-col md:flex-row gap-8">
          <aside className="w-full md:w-1/4 md:sticky md:top-4 self-start">
            <div className="bg-gray-50 rounded-lg p-5 border shadow-sm">
              <h3 className="font-semibold text-gray-800 mb-4 text-lg">Documentation</h3>
              <div className="space-y-1">
                <div className="font-medium text-blue-600 bg-blue-50 rounded px-3 py-2">
                  <a href="/docs/affiliate-program" className="block">Affiliate Program</a>
                </div>
                <div className="hover:bg-gray-100 rounded px-3 py-2 transition-colors">
                  <a href="/docs" className="block text-gray-700 hover:text-gray-900">API Reference</a>
                </div>
                <div className="hover:bg-gray-100 rounded px-3 py-2 transition-colors">
                  <a href="/docs" className="block text-gray-700 hover:text-gray-900">Getting Started</a>
                </div>
              </div>
              
              <h4 className="font-medium text-gray-700 mt-6 mb-2">On This Page</h4>
              <nav className="text-sm">
                <ul className="space-y-1 text-gray-600">
                  <li><a href="#introduction" className="hover:text-blue-600">Introduction</a></li>
                  <li><a href="#why-become-an-affiliate" className="hover:text-blue-600">Why Become an Affiliate</a></li>
                  <li><a href="#getting-started" className="hover:text-blue-600">Getting Started</a></li>
                  <li><a href="#accessing-listing-data" className="hover:text-blue-600">Accessing Data</a></li>
                  <li><a href="#utilizing-listing-data" className="hover:text-blue-600">Utilizing Data</a></li>
                </ul>
              </nav>
            </div>
          </aside>
          
          <main className="flex-1 pb-16">
            {/* Use the client component to render MDX content */}
            <MDXContent source={mdxSource} />
          </main>
        </div>
        
        <footer className="py-6 border-t mt-12">
          <div className="flex justify-between items-center">
            <p className="text-gray-600">
              &copy; {new Date().getFullYear()} adpro-clicks - All rights reserved
            </p>
            <div className="flex space-x-4">
              <a href="/terms" className="text-gray-600 hover:text-blue-600">Terms</a>
              <a href="/privacy" className="text-gray-600 hover:text-blue-600">Privacy</a>
              <a href="/contact" className="text-gray-600 hover:text-blue-600">Contact</a>
            </div>
          </div>
        </footer>
      </div>
    );
  } catch (error) {
    console.error('Error loading documentation:', error);
    // Use Next.js notFound() to show a 404 page
    notFound();
  }
}

// Generate static metadata for the page
export async function generateMetadata() {
  return {
    title: 'Affiliate Program | Documentation',
    description: 'Learn how to become an affiliate vendor on adpro-clicks and earn commissions by promoting our listings.',
  };
}
