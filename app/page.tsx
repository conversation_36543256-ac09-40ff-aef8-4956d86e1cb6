import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowRight, CheckCircle, Star, TrendingUp, Users } from "lucide-react"

export default function Home() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative">
        <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-primary/5 z-10" />
        <div className="container relative z-20 flex flex-col items-center justify-center px-4 py-24 text-center md:py-32">
          <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl">
            Find Top Affiliates to <span className="text-primary">Sell Your Products</span>
          </h1>
          <p className="mt-6 max-w-2xl text-lg text-muted-foreground md:text-xl">
            Connect with skilled affiliates who can boost your sales and grow your business. Our marketplace makes it
            easy to find, vet, and hire the perfect match for your products.
          </p>
          <div className="mt-8 flex flex-col gap-4 sm:flex-row">
            <Button asChild size="lg" className="gap-2">
              <Link href="/search">
                Explore Affiliates <ArrowRight className="h-4 w-4" />
              </Link>
            </Button>
            <Button asChild variant="outline" size="lg">
              <Link href="/listings">View Listings</Link>
            </Button>
          </div>
        </div>
        <div className="relative h-[300px] w-full md:h-[400px]">
          <Image
            src="/placeholder.svg?height=400&width=1200"
            alt="Business owner working with affiliate"
            fill
            className="object-cover"
            priority
          />
        </div>
      </section>

      {/* Affiliate Thumbnails */}
      <section className="bg-muted/50 py-16">
        <div className="container px-4">
          <div className="mb-10 text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">Top Performing Affiliates</h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Browse our selection of high-performing affiliates across various niches
            </p>
          </div>
          <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-4">
            {[
              {
                name: "Sarah Johnson",
                image: "/placeholder.svg?height=300&width=300",
                niche: "E-commerce",
                metric: "Average ROI: 215%",
                rating: 4.9,
                reviews: 124,
              },
              {
                name: "Michael Chen",
                image: "/placeholder.svg?height=300&width=300",
                niche: "Fashion",
                metric: "Conversion Rate: 18%",
                rating: 4.8,
                reviews: 98,
              },
              {
                name: "Emma Rodriguez",
                image: "/placeholder.svg?height=300&width=300",
                niche: "Tech",
                metric: "Monthly Sales: $45K+",
                rating: 4.7,
                reviews: 156,
              },
              {
                name: "David Kim",
                image: "/placeholder.svg?height=300&width=300",
                niche: "Health & Wellness",
                metric: "Audience Size: 500K+",
                rating: 4.9,
                reviews: 112,
              },
            ].map((affiliate, index) => (
              <Link href={`/affiliate/${index + 1}`} key={index}>
                <Card className="overflow-hidden transition-all hover:shadow-md">
                  <div className="aspect-square relative">
                    <Image
                      src={affiliate.image || "/placeholder.svg"}
                      alt={affiliate.name}
                      fill
                      className="object-cover transition-transform hover:scale-105"
                    />
                  </div>
                  <CardContent className="p-4">
                    <Badge className="mb-2">{affiliate.niche}</Badge>
                    <h3 className="font-semibold">{affiliate.name}</h3>
                    <div className="mt-1 flex items-center text-sm text-muted-foreground">
                      <Star className="mr-1 h-4 w-4 fill-primary text-primary" />
                      <span>
                        {affiliate.rating} ({affiliate.reviews} reviews)
                      </span>
                    </div>
                    <p className="mt-2 text-sm font-medium text-primary">{affiliate.metric}</p>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
          <div className="mt-10 text-center">
            <Button asChild variant="outline">
              <Link href="/search">View All Affiliates</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Why Hire Our Affiliates */}
      <section className="py-16">
        <div className="container px-4">
          <div className="mb-10 text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">Why Hire Our Affiliates?</h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Our marketplace connects you with pre-vetted professionals who can drive real results
            </p>
          </div>
          <div className="grid gap-8 md:grid-cols-3">
            <Card className="flex flex-col items-center p-6 text-center">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10 text-primary">
                <TrendingUp className="h-6 w-6" />
              </div>
              <h3 className="mt-4 text-xl font-semibold">Proven Results</h3>
              <p className="mt-2 text-muted-foreground">
                Our affiliates have a track record of success with measurable ROI and conversion rates.
              </p>
            </Card>
            <Card className="flex flex-col items-center p-6 text-center">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10 text-primary">
                <Users className="h-6 w-6" />
              </div>
              <h3 className="mt-4 text-xl font-semibold">Niche Expertise</h3>
              <p className="mt-2 text-muted-foreground">
                Find specialists who understand your industry and can connect with your target audience.
              </p>
            </Card>
            <Card className="flex flex-col items-center p-6 text-center">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10 text-primary">
                <CheckCircle className="h-6 w-6" />
              </div>
              <h3 className="mt-4 text-xl font-semibold">Transparent Process</h3>
              <p className="mt-2 text-muted-foreground">
                Clear communication, detailed analytics, and straightforward contracts with no hidden fees.
              </p>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16">
        <div className="container px-4">
          <div className="rounded-xl bg-primary/5 p-8 md:p-12">
            <div className="grid gap-8 md:grid-cols-2">
              <div>
                <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">Ready to grow your business?</h2>
                <p className="mt-4 text-lg text-muted-foreground">
                  Join thousands of businesses that have increased their sales through our affiliate marketplace.
                </p>
                <div className="mt-8">
                  <Button asChild size="lg">
                    <Link href="/search">Find Your Affiliate Today</Link>
                  </Button>
                </div>
              </div>
              <div className="flex flex-col justify-center">
                <div className="rounded-lg border bg-card p-6">
                  <h3 className="text-xl font-semibold">Stay updated</h3>
                  <p className="mt-2 text-sm text-muted-foreground">
                    Subscribe to our newsletter for the latest affiliate marketing trends and opportunities.
                  </p>
                  <div className="mt-4 flex gap-2">
                    <Input placeholder="Enter your email" className="flex-1" />
                    <Button>Subscribe</Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
